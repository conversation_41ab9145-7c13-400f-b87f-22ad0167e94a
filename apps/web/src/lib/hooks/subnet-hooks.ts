import { useQuery } from '@tanstack/react-query';
import { SubnetHistoryFrequency } from '@repo/types/website-api-types';
import type {
  DailyRegistrationRecycleParamsAtom,
  DistributionColdkeyQueryParams,
  DistributionIPQueryParams,
  NeuronDeregistrationQueryParams,
  NeuronRegistrationQueryParams,
  PoolHistoryQueryParams,
  RegistrationCostHistoryQueryParams,
  SubnetHistoryQueryParams,
  SubnetOwnerQueryParams,
  SubnetQueryParams,
} from '@repo/types/website-api-types';
import { apiClient, handleResponse } from '@/lib/api/api-client';
import { queryKeys } from '@/lib/constants/query-keys';

const subnetApiClient = apiClient.subnet;

export const useSubnet = (params: SubnetQueryParams = {}) => {
  return useQuery({
    queryKey: ['subnetData', params],
    queryFn: async () =>
      handleResponse(
        await subnetApiClient.subnet.$get({ query: { ...params } })
      ),
  });
};

export const useSubnetOwner = (params: SubnetOwnerQueryParams = {}) => {
  return useQuery({
    queryKey: ['subnetOwner', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const { page, limit, netuid, owner, order } =
        queryParams as SubnetOwnerQueryParams;
      if (!netuid && netuid !== 0) return [];

      return handleResponse(
        await subnetApiClient.subnetOwner.$get({
          query: { netuid, owner, page, limit, order },
        })
      );
    },
  });
};

export const useSubnetHistory = (
  params: SubnetHistoryQueryParams = { netuid: -1 }
) => {
  return useQuery({
    queryKey: ['subnetHistory', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const {
        netuid,
        limit = 100,
        frequency = SubnetHistoryFrequency.ByHour,
      } = queryParams as SubnetHistoryQueryParams;
      if (netuid === -1) return [];
      return handleResponse(
        await subnetApiClient.subnetHistory.$get({
          query: { netuid, limit, frequency },
        })
      );
    },
  });
};

export const useNeuronRegistration = (
  params: NeuronRegistrationQueryParams = {}
) => {
  return useQuery({
    queryKey: ['neuronRegistration', params],
    queryFn: async () =>
      handleResponse(
        await subnetApiClient.neuronRegistration.$get({ query: { ...params } })
      ),
  });
};

export const useSubnetRegistrationCostHistory = (
  params: RegistrationCostHistoryQueryParams = {}
) => {
  return useQuery({
    queryKey: ['subnetRegistrationCostHistory', params],
    queryFn: async () =>
      handleResponse(
        await subnetApiClient.subnetRegistrationCostHistory.$get({
          query: { ...params },
        })
      ),
  });
};

export const useDailyRegistrationRecycle = (
  params: DailyRegistrationRecycleParamsAtom = { limit: 183 }
) => {
  return useQuery({
    queryKey: ['dailyRegistrationRecycle', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const { limit } = queryParams as DailyRegistrationRecycleParamsAtom;
      return handleResponse(
        await subnetApiClient.dailyRegistrationRecycle.$get({
          query: { limit },
        })
      );
    },
  });
};

export const useDeregistrationHistory = (
  params: NeuronDeregistrationQueryParams = { netuid: -1 }
) => {
  return useQuery({
    queryKey: ['deregistrationHistory', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const { netuid, timestamp_start: timestampStart } =
        queryParams as NeuronDeregistrationQueryParams;

      if (netuid === -1) return [];

      return handleResponse(
        await subnetApiClient.deregistrationHistory.$get({
          query: { netuid, timestamp_start: timestampStart },
        })
      );
    },
  });
};

export const useSubnetEmission = (
  params: SubnetHistoryQueryParams = { netuid: -1 }
) => {
  return useQuery({
    queryKey: ['subnetEmission', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const {
        netuid,
        timestamp_start: timestampStart,
        frequency = SubnetHistoryFrequency.ByHour,
      } = queryParams as SubnetHistoryQueryParams;

      if (netuid === -1) return [];

      return handleResponse(
        await subnetApiClient.subnetEmission.$get({
          query: { netuid, timestamp_start: timestampStart, frequency },
        })
      );
    },
  });
};

export const useSubnetImmune = (
  params: NeuronRegistrationQueryParams = { netuid: -1 }
) => {
  return useQuery({
    queryKey: ['subnetImmune', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const { netuid, timestamp_start: timestampStart } =
        queryParams as NeuronRegistrationQueryParams;

      if (netuid === -1) return [];

      return handleResponse(
        await subnetApiClient.subnetImmune.$get({
          query: { netuid, timestamp_start: timestampStart },
        })
      );
    },
  });
};

export const useRootProportion = (
  params: PoolHistoryQueryParams = { netuid: -1 }
) => {
  return useQuery({
    queryKey: ['rootProportion', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const { netuid } = queryParams as PoolHistoryQueryParams;

      if (netuid === -1) return [];

      return handleResponse(
        await subnetApiClient.rootProportion.$get({
          query: { netuid },
        })
      );
    },
  });
};

export const useSubnetIdentityData = () => {
  return useQuery({
    queryKey: [queryKeys.subnetIdentities],
    queryFn: async () => handleResponse(await subnetApiClient.identity.$get()),
    refetchInterval: 60000, // 1 hour
  });
};

export const useTopSubnetEmission = () => {
  return useQuery({
    queryKey: ['topSubnetEmission'],
    queryFn: async () =>
      handleResponse(await subnetApiClient.topSubnetEmission.$get()),
  });
};

export const useDistributionColdkey = (
  params: DistributionColdkeyQueryParams = { netuid: -1 }
) => {
  return useQuery({
    queryKey: ['distributionColdkey', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const { netuid } = queryParams as DistributionColdkeyQueryParams;
      if (netuid === -1) return [];

      return handleResponse(
        await subnetApiClient.distributionColdkey.$get({
          query: { netuid },
        })
      );
    },
  });
};

export const useDistributionIp = (
  params: DistributionIPQueryParams = { netuid: -1 }
) => {
  return useQuery({
    queryKey: ['distributionColdkey', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const { netuid } = queryParams as DistributionIPQueryParams;
      if (netuid === -1) return [];

      return handleResponse(
        await subnetApiClient.distributionIp.$get({
          query: { netuid },
        })
      );
    },
  });
};
