import { Hono } from 'hono';
import type { SubnetHistoryFrequency } from '@repo/types/website-api-types';
import { fetchDtaoSubnetData } from '@/api-proxy/dtao';
import {
  fetchDailyRegistrationRecycleData,
  fetchDeregistration,
  fetchDistributionColdkey,
  fetchDistributionIP,
  fetchImmune,
  fetchNeuronRegistration,
  fetchRootProportionData,
  fetchSubnet,
  fetchSubnetEmissionData,
  fetchSubnetIdentity,
  fetchSubnetOwner,
  fetchSubnetRecycledData,
  fetchSubnetRegistrationCostHistory,
  fetchTopSubnetEmission,
} from '@/api-proxy/subnets';

const app = new Hono()
  .get('/subnet', async (c) => {
    const response = await fetchSubnet({ ...c.req.query() });
    return c.json(response);
  })
  .get('/subnetOwner', async (c) => {
    const response = await fetchSubnetOwner({ ...c.req.query() });
    return c.json(response);
  })
  .get('/subnetHistory', async (c) => {
    const netuid = Number(c.req.query().netuid);
    const limit = Number(c.req.query().limit);
    const frequency = c.req.query().frequency as
      | SubnetHistoryFrequency
      | undefined;
    const response = await fetchSubnetRecycledData({
      netuid,
      limit,
      frequency,
    });
    return c.json(response);
  })
  .get('/neuronRegistration', async (c) => {
    const response = await fetchNeuronRegistration({ ...c.req.query() });
    return c.json(response);
  })
  .get('/subnetRegistrationCostHistory', async (c) => {
    const response = await fetchSubnetRegistrationCostHistory({
      ...c.req.query(),
    });
    return c.json(response);
  })
  .get('/dailyRegistrationRecycle', async (c) => {
    const limit = Number(c.req.query().limit);
    const response = await fetchDailyRegistrationRecycleData({
      limit,
    });
    return c.json(response);
  })
  .get('/deregistrationHistory', async (c) => {
    const netuid = Number(c.req.query().netuid);
    const timestampStart = Number(c.req.query().timestamp_start);
    const response = await fetchDeregistration({
      netuid,
      timestamp_start: timestampStart,
    });
    return c.json(response);
  })
  .get('/subnetEmission', async (c) => {
    const netuid = Number(c.req.query().netuid);
    const timestampStart = Number(c.req.query().timestamp_start);
    const frequency = c.req.query().frequency as
      | SubnetHistoryFrequency
      | undefined;
    const response = await fetchSubnetEmissionData({
      netuid,
      timestamp_start: timestampStart,
      frequency,
    });
    return c.json(response);
  })
  .get('/subnetImmune', async (c) => {
    const netuid = Number(c.req.query().netuid);
    const timestampStart = Number(c.req.query().timestamp_start);
    const response = await fetchImmune({
      netuid,
      timestamp_start: timestampStart,
    });
    return c.json(response);
  })
  .get('/rootProportion', async (c) => {
    const netuid = Number(c.req.query().netuid);
    const response = await fetchRootProportionData({
      netuid,
    });
    return c.json(response);
  })
  .get('/dtaoSubnet', async (c) => {
    const response = await fetchDtaoSubnetData();
    return c.json(response);
  })
  .get('/identity', async (c) => {
    const response = await fetchSubnetIdentity({ ...c.req.query() });
    return c.json(response);
  })
  .get('/topSubnetEmission', async (c) => {
    const response = await fetchTopSubnetEmission();
    return c.json(response);
  })
  .get('/distributionColdkey', async (c) => {
    const netuid = Number(c.req.query().netuid);
    const response = await fetchDistributionColdkey({ netuid });
    return c.json(response);
  })
  .get('/distributionIp', async (c) => {
    const netuid = Number(c.req.query().netuid);
    const response = await fetchDistributionIP({ netuid });
    return c.json(response);
  });

export const subnetApi = app;
