import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { TableText } from '@repo/ui/components';

export const MetagraphVtrustCell = ({
  row,
}: CellContext<TableData, unknown>) => (
  <TableText
    u16Percentage
    className='flex w-14 justify-end'
    info={row.original.validator_trust as string}
  />
);

export const MetagraphVtrustHeader = () => (
  <p className='-mr-2 flex w-14 justify-end'>VTrust</p>
);
