import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { AddressFormatter } from '@repo/ui/components';

export const MetagraphNameCell = ({ row }: CellContext<TableData, unknown>) => (
  <div className='flex w-52'>
    <AddressFormatter
      info={row.original.hotkey as string}
      noColor={false}
      isEncoded
      isValidator
      className='text-sm'
      rootClassName='max-w-52'
      textClassName='flex-shrink-[1]'
      underline='shrink-[unset] min-w-0'
    />
  </div>
);

export const MetagraphNameHeader = () => (
  <span className='-mr-[118px] flex w-52'>Name</span>
);
