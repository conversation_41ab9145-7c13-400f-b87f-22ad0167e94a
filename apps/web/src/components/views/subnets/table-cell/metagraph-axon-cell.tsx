import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { TableText } from '@repo/ui/components';

export const MetagraphAxonCell = ({ row }: CellContext<TableData, unknown>) => (
  <TableText
    info={row.original.axon_info as string}
    className='w-38 flex justify-end'
  />
);

export const MetagraphAxonHeader = () => (
  <p className='w-38 -mr-2 flex justify-end'>Axon</p>
);
