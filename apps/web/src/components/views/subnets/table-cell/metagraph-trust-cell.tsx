import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { TableText } from '@repo/ui/components';

export const MetagraphTrustCell = ({
  row,
}: CellContext<TableData, unknown>) => (
  <TableText
    info={row.original.trust as string}
    dividedBy={1}
    className='flex w-14 justify-end'
  />
);

export const MetagraphTrustHeader = () => (
  <p className='-mr-2 flex w-14 justify-end'>Trust</p>
);
