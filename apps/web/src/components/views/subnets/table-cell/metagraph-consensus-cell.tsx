import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { TableText } from '@repo/ui/components';

export const MetagraphConsensusCell = ({
  row,
}: CellContext<TableData, unknown>) => (
  <TableText
    className='flex w-16 justify-end'
    info={row.original.consensus as string}
    dividedBy={1}
  />
);

export const MetagraphConsensusHeader = () => (
  <p className='-mr-2'>Consensus</p>
);
