import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { TableText } from '@repo/ui/components';

export const MetagraphUpdatedCell = ({
  row,
}: CellContext<TableData, unknown>) => (
  <TableText
    info={row.original.updated as string}
    className='flex w-16 justify-end'
  />
);

export const MetagraphUpdatedHeader = () => (
  <p className='-mr-2 flex w-16 justify-end'>Updated</p>
);
