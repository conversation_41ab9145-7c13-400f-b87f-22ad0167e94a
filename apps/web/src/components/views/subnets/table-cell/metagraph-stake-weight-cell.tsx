import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { Link } from '@repo/ui/components';
import { appRoutes } from '@repo/ui/lib';
import { SubnetDtaoWrapper } from '@/components/views/dtao/subnet-dtao-wrapper';

export const MetagraphStakeWeightCell = (
  context: CellContext<TableData, unknown>,
  symbol: string,
  subnetId: string
) => {
  const stake = context.row.original.stake as number;
  const isChild = context.row.original.is_child_key as boolean;
  const hotkey = context.row.original.hotkey as string;

  return isChild ? (
    <Link
      href={appRoutes.blockchain.hotkeyDetail(
        subnetId ? `${hotkey}#subnet${subnetId}` : hotkey
      )}
      className='flex w-28 justify-end decoration-yellow-300 hover:underline'
    >
      <SubnetDtaoWrapper
        info={stake}
        maximumFractionDigits={2}
        minimumFractionDigits={0}
        suffix={<p className='mr-1'>{symbol}</p>}
        className='flex w-28 justify-end text-yellow-300'
      />
    </Link>
  ) : (
    <SubnetDtaoWrapper
      info={stake}
      maximumFractionDigits={2}
      minimumFractionDigits={0}
      suffix={<p className='mr-1'>{symbol}</p>}
      className='text-fire flex w-28 justify-end'
    />
  );
};

export const MetagraphStakeWeightHeader = () => (
  <p className='w-30 -mr-2 flex justify-end'>Stake Weight (a)</p>
);
