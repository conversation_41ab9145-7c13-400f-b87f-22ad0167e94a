import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { SubnetDtaoWrapper } from '@/components/views/dtao/subnet-dtao-wrapper';

export const MetagraphRootWeightCell = (
  context: CellContext<TableData, unknown>,
  symbol: string
) => (
  <SubnetDtaoWrapper
    info={context.row.original.root_stake_as_alpha as number}
    maximumFractionDigits={0}
    minimumFractionDigits={0}
    suffix={<p className='mr-1'>{symbol}</p>}
    className='flex w-24 justify-end'
  />
);

export const MetagraphRootWeightHeader = () => (
  <p className='-mr-2 flex w-24 justify-end'>Root Weight</p>
);
