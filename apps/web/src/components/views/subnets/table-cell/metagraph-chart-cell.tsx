import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { DtaoPercentage } from '@/components/views/dtao/dtao-percentage';

export const MetagraphChartCell = ({
  row,
}: CellContext<TableData, unknown>) => (
  <DtaoPercentage
    root={row.original.root_stake_as_alpha as number}
    stake={row.original.alpha_stake as number}
    leftContent='Root'
    rightContent='Alpha'
    contentClassName='text-sm'
  />
);
