import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import UidWrapper from '@/components/elements/uid-wrapper';

export const MetagraphUidCell = (
  context: CellContext<TableData, unknown>,
  subnetId: string
) => (
  <UidWrapper
    uid={context.row.original.neuron_id as string}
    subnetId={subnetId}
    className='flex w-8 justify-end'
  />
);

export const MetagraphUidHeader = () => (
  <p className='-mr-2 flex w-8 justify-end'>UID</p>
);
