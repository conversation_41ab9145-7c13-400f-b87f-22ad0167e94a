import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { SubnetDtaoWrapper } from '@/components/views/dtao/subnet-dtao-wrapper';

export const MetagraphRootStakeCell = ({
  row,
}: CellContext<TableData, unknown>) => (
  <SubnetDtaoWrapper
    info={row.original.root_stake as number}
    maximumFractionDigits={0}
    minimumFractionDigits={0}
    currencyClassName='-mr-1'
    className='flex w-24 justify-end'
  />
);

export const MetagraphRootStakeHeader = () => (
  <p className='-mr-2 flex w-24 justify-end'>Root Stake</p>
);
