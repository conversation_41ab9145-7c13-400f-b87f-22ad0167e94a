import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { SubnetDtaoWrapper } from '@/components/views/dtao/subnet-dtao-wrapper';

export const MetagraphDailyAlphaCell = (
  context: CellContext<TableData, unknown>,
  symbol: string
) => (
  <SubnetDtaoWrapper
    info={context.row.original.daily_reward as number}
    maximumFractionDigits={2}
    minimumFractionDigits={0}
    suffix={<p className='mr-1'>{symbol}</p>}
    className='flex w-24 justify-end'
  />
);

export const MetagraphDailyAlphaHeader = () => (
  <p className='-mr-2 flex w-24 justify-end'>Daily Alpha</p>
);
