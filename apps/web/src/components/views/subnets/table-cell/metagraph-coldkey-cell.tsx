import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { KeyWrapper } from '@/components/elements/key-wrapper';

export const MetagraphColdkeyCell = ({
  row,
}: CellContext<TableData, unknown>) => (
  <KeyWrapper
    info={row.original.coldkey as string}
    linkToAccount
    className='text-sm'
  />
);

export const MetagraphColdkeyHeader = () => (
  <p className='w-17 -mr-2 flex justify-end'>Coldkey</p>
);
