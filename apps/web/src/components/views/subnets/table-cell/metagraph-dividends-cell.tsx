import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { TableText } from '@repo/ui/components';

export const MetagraphDividendsCell = ({
  row,
}: CellContext<TableData, unknown>) => (
  <TableText
    u16Percentage
    className='flex w-16 justify-end'
    info={row.original.dividends as string}
  />
);

export const MetagraphDividendsHeader = () => (
  <p className='-mr-2 flex w-16 justify-end'>Dividends</p>
);
