import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { KeyWrapper } from '@/components/elements/key-wrapper';

export const ColdkeyCell = (
  context: CellContext<TableData, unknown>,
  isMobile: boolean
) => (
  <KeyWrapper
    info={context.row.original.coldkey as string}
    fullWidth={!isMobile}
    isEncoded
    noCopy
  />
);
