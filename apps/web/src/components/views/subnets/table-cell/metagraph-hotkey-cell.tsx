import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { AddressFormatter } from '@repo/ui/components';
import { cn } from '@repo/ui/lib';

export const MetagraphHotkeyCell = (
  context: CellContext<TableData, unknown>,
  subnetId: string
) => {
  const hotkey = context.row.original.hotkey as string;
  const isChild = context.row.original.is_child_key as boolean;
  const isValidator = context.row.original.type === 'VALIDATOR';
  return (
    <AddressFormatter
      info={hotkey}
      noChange
      noIcon
      max={6}
      isHotkey
      isValidator={isValidator}
      isChildHotkey={isChild}
      className={cn('text-sm', isChild ? 'text-yellow-300' : '')}
      underline={isChild ? 'decoration-yellow-300' : ''}
      subnetId={subnetId}
    />
  );
};

export const MetagraphHotkeyHeader = () => (
  <p className='w-17 -mr-2 flex justify-end'>Hotkey</p>
);
