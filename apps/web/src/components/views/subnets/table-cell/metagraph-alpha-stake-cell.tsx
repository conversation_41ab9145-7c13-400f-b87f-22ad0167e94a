import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { SubnetDtaoWrapper } from '@/components/views/dtao/subnet-dtao-wrapper';

export const MetagraphAlphaStakeCell = (
  context: CellContext<TableData, unknown>,
  symbol: string
) => (
  <SubnetDtaoWrapper
    info={context.row.original.alpha_stake as number}
    maximumFractionDigits={0}
    minimumFractionDigits={0}
    suffix={<p className='mr-1'>{symbol}</p>}
    className='w-22 flex justify-end'
  />
);

export const MetagraphAlphaStakeHeader = () => (
  <p className='w-22 -mr-2 flex justify-end'>Alpha Stake</p>
);
