import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { TableText } from '@repo/ui/components';

export const MetagraphIncentiveCell = ({
  row,
}: CellContext<TableData, unknown>) => (
  <TableText
    className='flex w-14 justify-end'
    info={row.original.incentive as string}
    dividedBy={1}
  />
);

export const MetagraphIncentiveHeader = () => (
  <p className='-mr-2'>Incentive</p>
);
