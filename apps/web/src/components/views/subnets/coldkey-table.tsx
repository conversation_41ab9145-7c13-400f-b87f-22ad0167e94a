'use client';

import { memo, useCallback, useMemo, useState } from 'react';
import type { CellContext, SortingState } from '@tanstack/react-table';
import type { ColumnSchema, TableData } from '@repo/types/website-api-types';
import { BubbleTable } from '@repo/ui/components';
import { useWindowSize } from '@repo/ui/lib';
import { ColdkeyCell, ColdkeyMinerCell } from './table-cell';
import TablePagination from '@/components/views/blockchain/table-pagination';
import { useDistributionColdkey } from '@/lib/hooks';

const ITEMS_PER_PAGE = 10;
export const ColdkeyTable = memo(({ netuid }: { netuid: number }) => {
  const { data, isPending } = useDistributionColdkey({ netuid });
  const distribution = useMemo(
    () =>
      (Array.isArray(data) ? [] : data?.data ?? []).sort(
        (a, b) => b.count - a.count
      ),
    [data]
  );

  const { isMobile } = useWindowSize();
  const [currentPage, setCurrentPage] = useState(1);
  const [sorting, setSorting] = useState<SortingState>([]);

  const column: ColumnSchema[] = useMemo(
    () => [
      {
        id: 'coldkey',
        header: 'Coldkey',
        cell: (info: CellContext<TableData, unknown>) =>
          ColdkeyCell(info, isMobile),
      },
      {
        id: 'miners',
        header: 'Miner Count',
        cell: ColdkeyMinerCell,
      },
    ],
    [isMobile]
  );

  const tableData = useMemo<TableData[]>(() => {
    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
    const endIndex = startIndex + ITEMS_PER_PAGE;
    return distribution
      .sort((a, b) => {
        if (sorting[0]?.id === 'coldkey') {
          if (sorting[0].desc) {
            return b.coldkey.localeCompare(a.coldkey);
          }
          return a.coldkey.localeCompare(b.coldkey);
        }
        if (sorting[0]?.id === 'miners') {
          return sorting[0].desc ? b.count - a.count : a.count - b.count;
        }
        return 0;
      })
      .slice(startIndex, endIndex)
      .map((item) => ({
        coldkey: item.coldkey,
        miners: item.count,
      }));
  }, [distribution, currentPage, sorting]);

  const handleFilterChange = useCallback(
    (filter: string, sortingValue: SortingState) => {
      setSorting(sortingValue);
    },
    []
  );

  const totalCount = useMemo(() => {
    return distribution.length;
  }, [distribution]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  return (
    <div className='flex w-full flex-col gap-10 overflow-x-auto'>
      <div
        className='relative flex w-full flex-col gap-8'
        id='transaction-table'
      >
        <BubbleTable
          small
          columnSchemas={column}
          rowData={tableData}
          isFetching={isPending}
          onFilterChange={handleFilterChange}
        />
        <TablePagination onPageChange={handlePageChange} total={totalCount} />
      </div>
    </div>
  );
});

ColdkeyTable.displayName = 'Coldkey Table';
