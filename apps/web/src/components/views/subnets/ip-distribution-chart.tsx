'use client';
import { useState } from 'react';
import { Group } from '@visx/group';
import {
  Treemap,
  hierarchy,
  treemapSliceDice,
  treemapSquarify,
} from '@visx/hierarchy';
import { useScreenSize } from '@visx/responsive';
import { scaleLinear } from '@visx/scale';
import { useTooltip } from '@visx/tooltip';
import { Columns4, Grid } from 'lucide-react';
import { Button, Skeleton, Text } from '@repo/ui/components';
import { cn } from '@repo/ui/lib';
import { useDistributionIp } from '@/lib/hooks';
import { useSubnetHeaderOpen } from '@/lib/hooks/single-subnet-header';

const color1 = '#00DBBC';
const color2 = '#2C2E43';
const background = '#262626';

interface TooltipData {
  name: string;
  minersCount: number;
}

interface Data {
  name: string;
  ipAddress: string;
  minersCount: number;
  children?: Data[];
}

export const IpDistributionChart = ({ netuid }: { netuid: number }) => {
  const {
    tooltipOpen,
    tooltipLeft,
    tooltipTop,
    tooltipData,
    hideTooltip,
    showTooltip,
  } = useTooltip<TooltipData>();
  const { open } = useSubnetHeaderOpen();
  const [tileMethod, setTileMethod] = useState<string>('treemapSliceDice');
  const { data, isPending } = useDistributionIp({ netuid });

  const { width } = useScreenSize({ debounceTime: 150 });
  const controlWidth = width - (open ? 0 : width > 1152 ? 560 : 0);

  const height = 400;

  const tooltipWidth = 200;

  let tooltipTimeout: ReturnType<typeof setTimeout>;

  const hideTooltipWithDelay = () => {
    tooltipTimeout = setTimeout(hideTooltip, 200);
  };

  const cancelTooltipHide = () => {
    clearTimeout(tooltipTimeout);
  };

  const margin = { top: 0, left: 0, right: 0, bottom: 0 };
  const xMax = controlWidth - 80;
  const yMax = height;

  const distributionData = Array.isArray(data) ? [] : data?.data ?? [];

  const root = hierarchy<Data>({
    ipAddress: 'IP Distribution',
    minersCount: 0,
    name: 'IP Distribution',
    children: distributionData.map((item) => ({
      ipAddress: item.ip,
      minersCount: Number(item.count),
      name: item.ip,
    })),
  })
    .sum((d) => d.minersCount)
    .sort((a, b) => (b.value || 0) - (a.value || 0));

  const colorScale = scaleLinear<string>({
    domain: [0, Math.max(...distributionData.map((d) => Number(d.count)))],
    range: [color2, color1],
  });

  const tileMethods: Record<
    string,
    typeof treemapSquarify | typeof treemapSliceDice
  > = {
    treemapSquarify,
    treemapSliceDice,
  };

  return controlWidth < 10 ? null : (
    <>
      {isPending ? <Skeleton className='h-[450px]' /> : null}
      <div className={isPending ? 'hidden' : ''}>
        <div className='relative flex flex-col items-center'>
          <div className='mb-4 flex w-full justify-end'>
            <Button
              variant='empty'
              className={cn(
                tileMethod === 'treemapSquarify' && 'text-ocean',
                'gap-2'
              )}
              onClick={() => {
                setTileMethod('treemapSquarify');
              }}
            >
              <Grid size={14} />
              Squares
            </Button>
            <Button
              variant='empty'
              className={cn(
                tileMethod === 'treemapSliceDice' && 'text-ocean',
                'gap-2'
              )}
              onClick={() => {
                setTileMethod('treemapSliceDice');
              }}
            >
              <Columns4 size={14} />
              Linear
            </Button>
          </div>
          <svg width={controlWidth - 80} height={height}>
            <title>IP Distribution</title>

            <Treemap
              top={margin.top}
              root={root}
              size={[xMax, yMax]}
              tile={tileMethods[tileMethod]}
              round
            >
              {(treemap) => (
                <Group>
                  {treemap.leaves().map((leaf) => {
                    const nodeWidth = leaf.x1 - leaf.x0;
                    const nodeHeight = leaf.y1 - leaf.y0;
                    return (
                      <Group
                        key={`leaf-${leaf.data.ipAddress}`}
                        top={leaf.y0 + margin.top}
                        left={leaf.x0 + margin.left}
                      >
                        <rect
                          width={nodeWidth}
                          height={nodeHeight}
                          fill={colorScale(leaf.value || 0)}
                          stroke={background}
                          className='transition-colors duration-500 hover:brightness-125'
                          onMouseOver={(event) => {
                            const { x, y } =
                              event.currentTarget.getBoundingClientRect();
                            let adjustedLeft =
                              x - (open ? 0 : width > 1152 ? 560 : 0);
                            let adjustedTop = y - 200;

                            if (
                              x -
                                (open ? 0 : width > 1152 ? 560 : 0) +
                                tooltipWidth >
                              controlWidth
                            ) {
                              adjustedLeft =
                                x -
                                tooltipWidth -
                                (open ? 0 : width > 1152 ? 560 : 0) -
                                20;
                            }

                            if (y - 400 < 0) {
                              adjustedTop = y;
                            }

                            showTooltip({
                              tooltipLeft: adjustedLeft,
                              tooltipTop: adjustedTop,
                              tooltipData: {
                                name: leaf.data.ipAddress,
                                // @ts-expect-error - leaf.value is number but type expects unknown
                                minersCount: leaf.value,
                              },
                            });
                            cancelTooltipHide();
                          }}
                          onFocus={(event) => {
                            const { x, y } =
                              event.currentTarget.getBoundingClientRect();
                            let adjustedLeft = x;
                            let adjustedTop = y - 400;

                            if (x + tooltipWidth > controlWidth) {
                              adjustedLeft = x - tooltipWidth;
                            }

                            if (y - 400 < 0) {
                              adjustedTop = y;
                            }

                            showTooltip({
                              tooltipLeft: adjustedLeft,
                              tooltipTop: adjustedTop,
                              tooltipData: {
                                name: leaf.data.ipAddress,
                                minersCount: Number(leaf.value),
                              },
                            });
                            cancelTooltipHide();
                          }}
                          onMouseOut={hideTooltipWithDelay}
                          onBlur={hideTooltipWithDelay}
                        />
                      </Group>
                    );
                  })}
                </Group>
              )}
            </Treemap>
          </svg>
          {tooltipOpen && tooltipData ? (
            <div
              className='pointer-events-none absolute z-10 flex flex-col gap-0.5 border border-neutral-800 bg-neutral-800/50 p-4 backdrop-blur-xl'
              style={{ left: tooltipLeft, top: tooltipTop }}
              onMouseOver={cancelTooltipHide}
              onFocus={cancelTooltipHide}
              onMouseOut={hideTooltipWithDelay}
              onBlur={hideTooltipWithDelay}
            >
              <Text level='sm' className='max-w-60 font-medium opacity-40'>
                IP Address
              </Text>
              <Text className='flex items-center' level='sm'>
                {tooltipData.name}
              </Text>
              <Text level='sm' className='max-w-60 font-medium opacity-40'>
                Miners Count
              </Text>
              <Text className='flex items-center' level='sm'>
                {tooltipData.minersCount}
              </Text>
            </div>
          ) : null}
        </div>
      </div>
    </>
  );
};
