'use client';

import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import type { SortingState } from '@tanstack/react-table';
import type { ColumnSchema, TableData } from '@repo/types/website-api-types';
import { BubbleTable } from '@repo/ui/components';
import TablePagination from '@/components/views/blockchain/table-pagination';
import { useDistributionIp } from '@/lib/hooks';

const ITEMS_PER_PAGE = 10;

export const IpTable = memo(({ netuid }: { netuid: number }) => {
  const [total, setTotal] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [sorting, setSorting] = useState<SortingState>([]);
  const { data, isPending } = useDistributionIp({ netuid });

  const column = useMemo<ColumnSchema[]>(
    () => [
      {
        id: 'ipAddress',
        header: 'IP Address',
        cell: (info) => <TableText info={info} />,
        enableSorting: false,
      },
      {
        id: 'miners',
        header: 'Miner Count',
        cell: (info) => <TableText info={info} />,
      },
    ],
    []
  );

  const distributionData = useMemo(
    () => (Array.isArray(data) ? [] : data?.data ?? []),
    [data]
  );

  const distributionDataPagination = useMemo(
    () => (Array.isArray(data) ? [] : data?.pagination ?? []),
    [data]
  );

  const tableData = useMemo<TableData[]>(() => {
    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
    const endIndex = startIndex + ITEMS_PER_PAGE;
    return distributionData
      .sort((a, b) => {
        if (sorting[0]?.id === 'ipAddress') {
          if (sorting[0].desc) {
            return b.ip.localeCompare(a.ip);
          }
          return a.ip.localeCompare(b.ip);
        }
        if (sorting[0]?.id === 'miners') {
          return sorting[0].desc
            ? Number(b.count) - Number(a.count)
            : Number(a.count) - Number(b.count);
        }
        return 0;
      })
      .slice(startIndex, endIndex)
      .map((item) => ({
        ipAddress: item.ip,
        miners: item.count,
      }));
  }, [currentPage, distributionData, sorting]);

  const handleFilterChange = useCallback(
    (filter: string, sortingValue: SortingState) => {
      setSorting(sortingValue);
    },
    []
  );

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  useEffect(() => {
    if (distributionDataPagination.le) {
      setTotal(data.pagination.total_items);
    }
  }, [data?.pagination]);

  return (
    <div className='flex w-full flex-col gap-10 overflow-x-auto'>
      <div
        className='relative flex w-full flex-col gap-8'
        id='transaction-table'
      >
        <BubbleTable
          small
          columnSchemas={column}
          rowData={tableData}
          isFetching={isPending}
          onFilterChange={handleFilterChange}
        />
        <TablePagination onPageChange={handlePageChange} total={total} />
      </div>
    </div>
  );
});

IpTable.displayName = 'Ip Address Table';
