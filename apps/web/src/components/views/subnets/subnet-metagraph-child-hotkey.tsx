import { useMemo } from 'react';
import type { ColumnDef } from '@tanstack/react-table';
import type { ColumnSchema, SubnetChildHotkey, TableData } from '@repo/types/website-api-types';
import { BubbleTable } from '@repo/ui/components';

export const SubnetMetagraphChildHotkey = ({
  tableData,
  childData,
  symbol,
}: {
  tableData: SubnetChildHotkey[];
  childData: SubnetChildHotkey | undefined;
  symbol?: string;
}) => {
  const childColumns = useMemo<ColumnSchema[]>(
    () => [
      {
        id: 'hotkey',
        header: () => <p className='ml-5 w-72'>Child</p>,
        cell: (info) => (
          <div className='flex w-72'>
            <AddressFormatter
              info={info}
              noIcon
              max={15}
              className='text-sm'
              rootClassName='max-w-72'
              textClassName='flex-shrink-[1]'
              underline='shrink-[unset] min-w-0'
            />
          </div>
        ),
        enableSorting: false,
      },
      {
        id: 'stake',
        header: () => (
          <p className='-mr-2 flex w-32 justify-end'>Stake Weight</p>
        ),
        cell: (info) => (
          <SubnetDtaoWrapper
            info={info}
            maximumFractionDigits={2}
            minimumFractionDigits={0}
            suffix={<p className='text-fire mr-1'>{symbol}</p>}
            className='text-fire flex w-32 justify-end'
          />
        ),
        enableSorting: false,
      },
      {
        id: 'root_weight',
        header: () => <p className='-mr-2 flex w-32 justify-end'>Root Prop</p>,
        cell: (info) => (
          <SubnetDtaoWrapper
            info={info}
            maximumFractionDigits={2}
            minimumFractionDigits={0}
            currencyClassName='-mr-1'
            className='flex w-32 justify-end'
          />
        ),
        enableSorting: false,
      },
      {
        id: 'alpha_weight',
        header: () => <p className='-mr-2 flex w-32 justify-end'>Alpha Prop</p>,
        cell: (info) => (
          <SubnetDtaoWrapper
            info={info}
            maximumFractionDigits={2}
            minimumFractionDigits={0}
            suffix={<p className='mr-1'>{symbol}</p>}
            className='flex w-32 justify-end'
          />
        ),
        enableSorting: false,
      },
      {
        id: 'netUid',
        header: '',
        cell: (info) => (
          <DtaoPercentage
            root={info.row.original.root_stake_as_alpha}
            stake={info.row.original.alpha_weight}
            leftContent='Root'
            rightContent='Alpha'
            contentClassName='text-sm'
          />
        ),
        enableSorting: false,
      },
      {
        id: 'take',
        header: () => <p className='flex w-11 justify-end'>Take</p>,
        cell: (info) => (
          <TableText
            amount={info.getValue() * 100}
            className='flex w-11 justify-end'
            percentage
          />
        ),
        enableSorting: false,
      },
      {
        id: 'proportion',
        header: 'Proportion',
        cell: (info) => (
          <TableText
            amount={info.getValue() * 100}
            className='flex w-14 justify-end'
            percentage
          />
        ),
        enableSorting: false,
      },
    ],
    [symbol]
  );

  const parentColumns: ColumnDef<any, any>[] = useMemo<ColumnSchema[]>(
    () => [
      {
        id: 'hotkey',
        header: () => <p className='ml-5 w-72'>Parent</p>,
        cell: (info) => (
          <div className='flex w-72'>
            <AddressFormatter
              info={info}
              noIcon
              max={15}
              className='text-sm'
              rootClassName='max-w-72'
              textClassName='flex-shrink-[1]'
              underline='shrink-[unset] min-w-0'
            />
          </div>
        ),
        enableSorting: false,
      },
      {
        id: 'stake',
        header: childData
          ? ''
          : () => <p className='-mr-2 flex w-32 justify-end'>Stake Weight</p>,
        cell: (info) => (
          <SubnetDtaoWrapper
            info={info}
            maximumFractionDigits={2}
            minimumFractionDigits={0}
            suffix={<p className='text-fire mr-1'>{symbol}</p>}
            className='text-fire flex w-32 justify-end'
          />
        ),
        enableSorting: false,
      },
      {
        id: 'root_weight',
        header: childData
          ? ''
          : () => <p className='-mr-2 flex w-32 justify-end'>Root Stake</p>,
        cell: (info) => (
          <SubnetDtaoWrapper
            info={info}
            maximumFractionDigits={2}
            minimumFractionDigits={0}
            currencyClassName='-mr-1'
            className='flex w-32 justify-end'
          />
        ),
        enableSorting: false,
      },
      {
        id: 'alpha_weight',
        header: childData
          ? ''
          : () => <p className='-mr-2 flex w-32 justify-end'>Alpha</p>,
        cell: (info) => (
          <SubnetDtaoWrapper
            info={info}
            maximumFractionDigits={2}
            minimumFractionDigits={0}
            suffix={<p className='mr-1'>{symbol}</p>}
            className='flex w-32 justify-end'
          />
        ),
        enableSorting: false,
      },
      {
        id: 'netUid',
        header: '',
        cell: (info) => (
          <DtaoPercentage
            root={info.row.original.root_stake_as_alpha}
            stake={info.row.original.alpha_weight}
            leftContent='Root'
            rightContent='Alpha'
            contentClassName='text-sm'
          />
        ),
        enableSorting: false,
      },
      {
        id: 'take',
        header: childData
          ? ''
          : () => <p className='flex w-11 justify-end'>Take</p>,
        cell: (info) => (
          <TableText
            amount={info.getValue() * 100}
            className='flex w-11 justify-end'
            percentage
          />
        ),
        enableSorting: false,
      },
      {
        id: 'proportion',
        header: childData ? '' : 'Proportion',
        cell: (info) => (
          <TableText
            amount={info.getValue() * 100}
            className='flex w-14 justify-end'
            percentage
          />
        ),
        enableSorting: false,
      },
    ],
    [childData, symbol]
  );

  const childTableData = useMemo<TableData[]>(
    () => (childData ? [{ ...childData }] : []),
    [childData]
  );

  const parentTableData = useMemo<TableData[]>(
    () =>
      tableData
        .map((item) => ({ ...item }))
        .sort((a, b) => Number(b.stake) - Number(a.stake)),
    [tableData]
  );

  return (
    <div className='rounded-xl bg-[#0D0D0D]'>
      <div className='max-w-7xl'>
        {childData ? (
          <BubbleTable
            columnSchemas={childColumns}
            rowData={childTableData}
            csvFilename='child-hotkey'
            initialSortBy={[{ id: 'stake', desc: true }]}
          />
        ) : null}
        <BubbleTable
          columnSchemas={parentColumns}
          rowData={parentTableData}
          csvFilename='child-hotkey'
          initialSortBy={[{ id: 'stake', desc: true }]}
        />
      </div>
    </div>
  );
};
