'use client';

import { use<PERSON><PERSON><PERSON>, useEffect, useMemo, useState } from 'react';
import type { CellContext, SortingState } from '@tanstack/react-table';
import { Pickaxe, Shield } from 'lucide-react';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { FaExclamationTriangle } from 'react-icons/fa';
import type {
  ColumnSchema,
  DtaoSubnet,
  MetagraphParamsAtom,
  Subnet,
  TableData,
} from '@repo/types/website-api-types';
import { MetagraphOrder } from '@repo/types/website-api-types';
import { Switch, Text } from '@repo/ui/components';
import { HeaderDescriptionCard } from './header-description-card';
import { SubnetMetagraphTable } from './subnet-metagraph-table';
import {
  MetagraphAlphaStakeCell,
  MetagraphAlphaStakeHeader,
  MetagraphAxonCell,
  MetagraphAxonHeader,
  MetagraphChartCell,
  MetagraphColdkeyCell,
  MetagraphColdkeyHeader,
  MetagraphConsensusCell,
  MetagraphConsensusHeader,
  MetagraphDailyAlphaCell,
  MetagraphDailyAlphaHeader,
  MetagraphDividendsCell,
  MetagraphDividendsHeader,
  MetagraphEmissionCell,
  MetagraphEmissionHeader,
  MetagraphHotkeyCell,
  MetagraphHotkeyHeader,
  MetagraphIncentiveCell,
  MetagraphIncentiveHeader,
  MetagraphNameCell,
  MetagraphNameHeader,
  MetagraphPosCell,
  MetagraphPosHeader,
  MetagraphRootStakeCell,
  MetagraphRootStakeHeader,
  MetagraphRootWeightCell,
  MetagraphRootWeightHeader,
  MetagraphStakeWeightCell,
  MetagraphStakeWeightHeader,
  MetagraphTrustCell,
  MetagraphTrustHeader,
  MetagraphTypeCell,
  MetagraphTypeHeader,
  MetagraphUidCell,
  MetagraphUidHeader,
  MetagraphUpdatedCell,
  MetagraphUpdatedHeader,
  MetagraphVtrustCell,
  MetagraphVtrustHeader,
} from './table-cell';
import TablePagination from '@/components/views/blockchain/table-pagination';
import { REFETCH_INTERVAL } from '@/lib/config';
import { useMetagraph } from '@/lib/hooks';
import { getFilter, getSorting } from '@/lib/utils';
import { useLatestPriceAtom } from '@/store/use-latest-price';

const searchFields = ['neuron_id', 'hotkey', 'coldkey'];

export function MetaGraphTable({
  subnetId,
  dtaoSubnet,
  currentSubnet,
}: {
  subnetId: string;
  dtaoSubnet?: DtaoSubnet | undefined;
  currentSubnet: Subnet | undefined;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const [page, setPage] = useState<number>(1);
  const [limit, setLimit] = useState<number>(25);
  const [sorting, setSorting] = useState<SortingState>([
    {
      id: 'stake',
      desc: true,
    },
  ]);
  const [filter, setFilter] = useState<string>('');

  const [metagraphParams, setMetagraphParams] = useState<MetagraphParamsAtom>({
    subnetId: Number(subnetId),
    order: MetagraphOrder.StakeDesc,
  });

  const { latestPrice: taoValue } = useLatestPriceAtom();

  const { data: metagraph, isPending } = useMetagraph(metagraphParams);

  const titleLabel =
    currentSubnet && !currentSubnet.registration_allowed ? (
      <div className='inline-flex max-w-fit items-start gap-2 rounded border border-red-900 bg-red-800/10 px-2 py-1'>
        <Text level='xs' className='flex gap-2 text-red-400'>
          <FaExclamationTriangle
            size={14}
            className='rounded-full bg-rose-500/10 text-rose-500'
          />
          Registration disabled
        </Text>
      </div>
    ) : null;

  const subnetMetagraph = useMemo(() => {
    return (
      metagraph?.data
        .map(
          (
            {
              block_number,
              uid,
              netuid,
              stake,
              trust,
              validator_trust,
              consensus,
              incentive,
              dividends,
              emission,
              registered_at_block,
              active,
              hotkey,
              coldkey,
              axon,
              is_immunity_period,
              updated,
              is_child_key,
              root_stake,
              alpha_stake,
              total_alpha_stake,
              root_stake_as_alpha,
              daily_reward,
            },
            index: number
          ) => ({
            block_number,
            subnet_id: netuid,
            neuron_id: uid,
            stake: Number(total_alpha_stake),
            total: (Number(total_alpha_stake) * taoValue) / taoDivider,
            trust: Number(trust),
            validator_trust: Number(validator_trust),
            type:
              Number(validator_trust) > 0
                ? 'VALIDATOR'
                : is_immunity_period
                  ? 'IMMUNE MINER'
                  : 'MINER',
            consensus: Number(consensus),
            incentive,
            dividends: Number(dividends),
            emission: Number(emission),
            registered_at_block,
            active,
            hotkey: hotkey.ss58,
            coldkey: coldkey.ss58,
            updated,
            axon_info: axon?.ip ? `${axon.ip}:${axon.port}` : '0.0.0.0',
            is_immunity_period,
            position: index + 1,
            is_child_key,
            root_stake: Number(root_stake),
            alpha_stake: Number(alpha_stake),
            total_alpha_stake: Number(root_stake_as_alpha),
            root_stake_as_alpha: Number(root_stake_as_alpha),
            daily_reward: Number(daily_reward),
          })
        )
        .filter((item) => getFilter(item, filter, searchFields))
        .sort((a, b) => getSorting(a, b, sorting)) ?? []
    );
  }, [isInit, initialData, metagraph, taoValue, filter, sorting]);

  const tableData = useMemo(() => {
    return (
      subnetMetagraph
        .map((item, index) => ({ ...item, position: index + 1 }))
        .slice((page - 1) * limit, page * limit) ?? []
    );
  }, [subnetMetagraph, page, limit]);

  const totalCount = useMemo(() => subnetMetagraph.length, [subnetMetagraph]);

  const columns = useMemo<ColumnSchema[]>(
    () => [
      {
        id: 'position',
        header: MetagraphPosHeader,
        cell: MetagraphPosCell,
      },
      {
        id: 'type',
        header: MetagraphTypeHeader,
        cell: MetagraphTypeCell,
      },
      {
        id: 'neuron_id',
        header: MetagraphUidHeader,
        cell: (info: CellContext<TableData, unknown>) =>
          MetagraphUidCell(info, subnetId),
      },
      {
        id: 'name',
        header: MetagraphNameHeader,
        cell: MetagraphNameCell,
        enableSorting: false,
      },
      {
        id: 'stake',
        header: MetagraphStakeWeightHeader,
        cell: (info: CellContext<TableData, unknown>) =>
          MetagraphStakeWeightCell(info, dtaoSubnet?.symbol ?? '', subnetId),
        sortingFn: 'alphanumeric',
      },
      {
        id: 'validator_trust',
        header: MetagraphVtrustHeader,
        cell: MetagraphVtrustCell,
      },
      {
        id: 'trust',
        header: MetagraphTrustHeader,
        cell: MetagraphTrustCell,
      },
      {
        id: 'consensus',
        header: MetagraphConsensusHeader,
        cell: MetagraphConsensusCell,
      },
      {
        id: 'incentive',
        header: MetagraphIncentiveHeader,
        cell: MetagraphIncentiveCell,
      },
      {
        id: 'dividends',
        header: MetagraphDividendsHeader,
        cell: MetagraphDividendsCell,
      },
      {
        id: 'emission',
        header: MetagraphEmissionHeader,
        cell: (info: CellContext<TableData, unknown>) =>
          MetagraphEmissionCell(info, dtaoSubnet?.symbol ?? ''),
      },
      {
        id: 'updated',
        header: MetagraphUpdatedHeader,
        cell: MetagraphUpdatedCell,
      },
      {
        id: 'axon_info',
        header: MetagraphAxonHeader,
        cell: MetagraphAxonCell,
      },
      {
        id: 'hotkey',
        header: MetagraphHotkeyHeader,
        cell: (info: CellContext<TableData, unknown>) =>
          MetagraphHotkeyCell(info, subnetId),
      },
      {
        id: 'coldkey',
        header: MetagraphColdkeyHeader,
        cell: MetagraphColdkeyCell,
      },
      {
        id: 'root_stake',
        header: MetagraphRootStakeHeader,
        cell: MetagraphRootStakeCell,
      },
      {
        id: 'root_stake_as_alpha',
        header: MetagraphRootWeightHeader,
        cell: (info: CellContext<TableData, unknown>) =>
          MetagraphRootWeightCell(info, dtaoSubnet?.symbol ?? ''),
      },
      {
        id: 'alpha_stake',
        header: MetagraphAlphaStakeHeader,
        cell: (info: CellContext<TableData, unknown>) =>
          MetagraphAlphaStakeCell(info, dtaoSubnet?.symbol ?? ''),
      },
      {
        id: 'netUid',
        header: () => <></>,
        cell: MetagraphChartCell,
        enableSorting: false,
      },
      {
        id: 'daily_reward',
        header: MetagraphDailyAlphaHeader,
        cell: (info: CellContext<TableData, unknown>) =>
          MetagraphDailyAlphaCell(info, dtaoSubnet?.symbol ?? ''),
      },
    ],
    [dtaoSubnet, subnetId]
  );

  const changeSearchParams = useCallback(
    ({
      filter: filterValue,
      sorting: sortingValue,
      limit: limitValue,
    }: {
      filter?: string;
      sorting?: string;
      limit?: number;
    }) => {
      const params = new URLSearchParams(searchParams);

      if (filterValue) {
        if (filterValue.length > 0) {
          params.set('filter', filterValue);
        } else if (params.get('filter')?.length) {
          params.delete('filter');
        }
      } else {
        params.delete('filter');
      }
      if (sortingValue) {
        params.set('order', sortingValue);
      }
      if (limitValue !== undefined) {
        params.set('limit', `${limitValue}`);
      }

      router.replace(`${pathname}?${params.toString()}`, { scroll: false });
    },
    [router, pathname, searchParams]
  );

  const handleFilterChange = useCallback(
    (filterValue: string, sortingValue: SortingState) => {
      setSorting(sortingValue);
      setFilter(filterValue);
      setPage(1);

      const newSorting =
        sortingValue.length > 0
          ? `${sortingValue[0].id}:${sortingValue[0].desc ? 'desc' : 'asc'}`
          : undefined;
      changeSearchParams({ filter: filterValue, sorting: newSorting });
    },
    [changeSearchParams]
  );

  const handlePageChange = useCallback((value: number) => {
    setPage(value);
  }, []);

  const handleLimitChange = useCallback(
    (value: number) => {
      setLimit(value);
      setPage(1);

      changeSearchParams({ limit: value });
    },
    [changeSearchParams]
  );

  useEffect(() => {
    setMetagraphParams({
      subnetId: Number(subnetId),
      order: MetagraphOrder.StakeDesc,
    });
  }, [subnetId, setMetagraphParams]);

  useEffect(() => {
    const pageParam = searchParams.get('page');
    const limitParam = searchParams.get('limit');
    const order = searchParams.get('order');

    if (pageParam) setPage(Number(pageParam));
    if (limitParam) setLimit(Number(limitParam));
    if (order)
      setSorting([
        {
          id: order.split(':')[0],
          desc: order.split(':')[1] === 'desc',
        },
      ]);
  }, [searchParams]);

  return (
    <div className='flex flex-col gap-4'>
      <HeaderDescriptionCard
        title='Subnet'
        titleLabel={titleLabel}
        secondaryTitle='Metagraph'
        description='The Subnet metagraph is a chart that displays a detailed readout of the neurons (validators & miners) on a subnet.'
      />
      <div className='flex flex-wrap justify-between gap-6'>
        <div className='flex flex-wrap gap-6'>
          <Text level='sm' className='flex items-center gap-2'>
            <span className='flex w-fit items-center gap-2 rounded-lg bg-white/[0.04] px-3 py-2'>
              <Shield size={14} className='text-indigo-400' />
            </span>
            <span className='opacity-40'>are validators.</span>
          </Text>
          <Text level='sm' className='flex items-center gap-2'>
            <span className='flex w-fit items-center gap-2 rounded-lg bg-white/[0.04] px-3 py-2'>
              <Pickaxe size={14} className='text-[#00DBBC]' />
            </span>
            <span className='opacity-40'>are miners.</span>
          </Text>
          <Text level='sm' className='flex items-center gap-2'>
            <span className='flex w-fit items-center gap-2 rounded-lg bg-white/[0.04] px-3 py-2'>
              <Pickaxe size={14} className='text-[#F90]' />
            </span>
            <span className='opacity-40'>are immune miners.</span>
          </Text>
        </div>
        <div className='flex items-center gap-1'>
          <Switch
            className='h-4 w-7'
            thumbClassName='h-3 w-3 data-[state=checked]:translate-x-3'
            checked={REFETCH_INTERVAL === metagraphParams.refetchInterval}
            onCheckedChange={(e) => {
              e
                ? setMetagraphParams((prev) => ({
                    ...prev,
                    refetchInterval: REFETCH_INTERVAL,
                  }))
                : setMetagraphParams((prev) => ({
                    ...prev,
                    refetchInterval: REFETCH_INTERVAL * 100,
                  }));
            }}
          />
          <Text level='sm' className='opacity-40'>
            Auto Refresh
          </Text>
        </div>
      </div>
      <SubnetMetagraphTable
        stickyHeader
        stickyHeaderClass='bg-[#121212]'
        selector
        searchable
        closeIcon
        columnSchemas={columns}
        rowData={tableData}
        onFilterChange={handleFilterChange}
        onLimitChange={handleLimitChange}
        initialPageSize={limit}
        isFetching={isPending}
        csvFilename={`Metagraph-SN${subnetId}`}
        rowTotalData={subnetMetagraph}
        symbol={dtaoSubnet?.symbol}
      />
      <TablePagination
        total={totalCount}
        defaultPageSize={limit}
        currentPage={page}
        onPageChange={handlePageChange}
      />
    </div>
  );
}
