import { ColdkeyDistributionChartDynamic } from './coldkey-distribution-chart.dynamic';
import { ColdkeyTable } from './coldkey-table';
import { HeaderDescriptionCard } from './header-description-card';
import { IpDistributionChartDynamic } from './ip-distribution-chart.dynamic';
import { IpTable } from './ip-table';

export function Distribution({ params: { id } }: { params: { id: string } }) {
  return (
    <>
      <div className='space-y-4'>
        <HeaderDescriptionCard
          title='Subnet'
          secondaryTitle='Distribution Data'
          description='This chart shows the incentive values for all miners on the subnet. The lowest active key shows where the cutoff is for active miners, and the next UID to be expelled on new registration. The shape of the chart is an indication of how competitive a subnet is (a narrow distribution indicates a competitive subnet where all miners are at risk of re-registration.'
        />
        {/* <DistributionChart id={id} /> */}
        {/* <DistributionChartWrapper id={id} /> */}
      </div>

      <div className='space-y-4'>
        <HeaderDescriptionCard
          title='Subnet'
          secondaryTitle='Miner Coldkey Distribution'
          description='Breakdown of Miners by Coldkey addresses.'
        />
        <ColdkeyGroup id={id} />
      </div>

      <div className='space-y-4'>
        <HeaderDescriptionCard
          title='Subnet'
          secondaryTitle='Miner IP Distribution'
          description='Breakdown of Miners by IP addresses.'
        />
        <IPDistributionGroup id={id} />
      </div>
    </>
  );
}

export function ColdkeyGroup({ id }: { id: string }) {
  return (
    <>
      <ColdkeyDistributionChartDynamic netuid={Number(id)} />
      <ColdkeyTable netuid={Number(id)} />
    </>
  );
}

export function IPDistributionGroup({ id }: { id: string }) {
  return (
    <>
      <IpDistributionChartDynamic netuid={Number(id)} />
      <IpTable netuid={Number(id)} />
    </>
  );
}
