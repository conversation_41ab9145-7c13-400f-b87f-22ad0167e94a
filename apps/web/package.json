{"name": "web", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev --port 3001", "build": "next build", "start": "next start --port 3001", "start:prod": "node_modules/next/dist/bin/next start --port 3001", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@number-flow/react": "^0.4.1", "@repo/ui": "workspace:*", "@tanstack/match-sorter-utils": "^8.19.4", "@tanstack/react-query": "^5.62.0", "@tanstack/react-query-devtools": "^5.62.0", "@tanstack/react-table": "^8.20.6", "@visx/axis": "^3.12.0", "@visx/curve": "^3.12.0", "@visx/event": "^3.12.0", "@visx/gradient": "^3.12.0", "@visx/grid": "^3.12.0", "@visx/group": "^3.12.0", "@visx/heatmap": "^3.12.0", "@visx/hierarchy": "^3.12.0", "@visx/mock-data": "^3.12.0", "@visx/responsive": "^3.12.0", "@visx/scale": "^3.12.0", "@visx/shape": "^3.12.0", "@visx/tooltip": "^3.12.0", "@visx/xychart": "^3.12.0", "@visx/zoom": "^3.12.0", "axios": "^1.7.8", "bignumber.js": "^9.1.2", "clsx": "^2.1.1", "d3": "^7.9.0", "date-fns": "^4.1.0", "emoji-regex": "^10.4.0", "framer-motion": "^12.16.0", "generate-avatar": "^1.4.10", "hono": "^4.6.15", "jotai": "^2.10.3", "jotai-tanstack-query": "^0.9.0", "js-cookie": "^3.0.5", "lucide-react": "^0.479.0", "moment": "^2.30.1", "next": "^14.2.3", "next-auth": "5.0.0-beta.25", "nextjs-toploader": "^3.8.16", "numerable": "^0.3.15", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.3.0", "react-top-loading-bar": "^3.0.2", "tailwind-merge": "^2.5.4"}, "devDependencies": {"@next/eslint-plugin-next": "^14.2.3", "@repo/config": "workspace:*", "@repo/eslint-config": "workspace:*", "@repo/types": "workspace:*", "@types/node": "^20.10.6", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "postcss": "^8.4.33", "tailwindcss": "^3.4.0", "tailwindcss-animate": "^1.0.6", "typescript": "^5.3.3", "zod": "^3.23.8"}}